# Database Setup for Campus Events

## Required Tables

### 1. User Event Responses Table

You need to create the `user_event_responses` table in your Supabase database. Run this SQL in your Supabase SQL editor:

```sql
-- Create enum for response types
CREATE TYPE response_type AS ENUM ('going', 'not_going', 'maybe', 'volunteer');

-- Create user_event_responses table
CREATE TABLE public.user_event_responses (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  user_id uuid NOT NULL,
  event_id uuid NOT NULL,
  response_type response_type NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  
  CONSTRAINT user_event_responses_pkey PRIMARY KEY (id),
  CONSTRAINT user_event_responses_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  CONSTRAINT user_event_responses_event_id_fkey FOREIGN KEY (event_id) REFERENCES events (id) ON DELETE CASCADE,
  CONSTRAINT user_event_responses_unique_user_event UNIQUE (user_id, event_id)
) TABLESPACE pg_default;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_event_responses_user_id ON public.user_event_responses USING btree (user_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_user_event_responses_event_id ON public.user_event_responses USING btree (event_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_user_event_responses_response_type ON public.user_event_responses USING btree (response_type) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_user_event_responses_created_at ON public.user_event_responses USING btree (created_at) TABLESPACE pg_default;

-- Create trigger to update updated_at column
CREATE TRIGGER update_user_event_responses_updated_at 
  BEFORE UPDATE ON user_event_responses 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE public.user_event_responses ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own responses
CREATE POLICY "Users can view their own responses" ON public.user_event_responses
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own responses
CREATE POLICY "Users can insert their own responses" ON public.user_event_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own responses
CREATE POLICY "Users can update their own responses" ON public.user_event_responses
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own responses
CREATE POLICY "Users can delete their own responses" ON public.user_event_responses
  FOR DELETE USING (auth.uid() = user_id);

-- Policy: Club owners can view responses to their events
CREATE POLICY "Club owners can view responses to their events" ON public.user_event_responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM events 
      WHERE events.id = user_event_responses.event_id 
      AND events.created_by = auth.uid()
    )
  );

-- Grant permissions
GRANT ALL ON public.user_event_responses TO authenticated;
GRANT SELECT ON public.user_event_responses TO anon;
```

### 2. Verify Your Events Table

Make sure your events table has the correct structure. If you need to update it, here's the expected schema:

```sql
-- Verify events table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'events' 
ORDER BY ordinal_position;
```

## What's Changed

### Frontend Updates

1. **Real Data Fetching**: The CampusEvents component now fetches real data from your Supabase `events` table instead of using mock data.

2. **User Responses**: Users can now RSVP to events (Going, Not Going, Maybe, Volunteer) and their responses are stored in the database.

3. **Real-time Stats**: The stats panel shows actual counts from the database.

4. **Loading States**: Added proper loading and error handling.

### Key Features

- ✅ Fetches events from Supabase `events` table
- ✅ Displays events with club information
- ✅ Allows users to RSVP to events
- ✅ Stores user responses in `user_event_responses` table
- ✅ Updates attendee counts automatically
- ✅ Shows real-time statistics
- ✅ Proper loading and error states
- ✅ Filtering by categories, event types, and search
- ✅ Responsive design

### Data Flow

1. **Events Loading**: Component fetches events from `events` table with club information
2. **User Responses**: When user clicks RSVP buttons, response is saved to `user_event_responses` table
3. **Attendee Count**: Automatically updates `attendees_count` in events table when users RSVP as "going"
4. **Stats**: Real-time calculation of total, upcoming, and past events

### Testing

1. Create some test events using your CreateEventForm
2. Navigate to Campus Events page
3. Try RSVPing to events
4. Check that responses are saved in the database
5. Verify attendee counts update correctly

## Troubleshooting

If you encounter issues:

1. **Check Supabase Connection**: Ensure your environment variables are set correctly
2. **Database Permissions**: Verify RLS policies are working
3. **Table Structure**: Ensure all required tables exist with correct schemas
4. **Console Errors**: Check browser console for any JavaScript errors

The component will gracefully handle missing data and show appropriate error messages if the database is not accessible.
