.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.toast {
  position: relative;
  min-width: 320px;
  padding: 14px 20px;
  border-radius: 8px;
  color: #111827;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.toast.success {
  background: #d1fae5;
  border-left: 5px solid #10b981;
}

.toast.info {
  background: #dbeafe;
  border-left: 5px solid #3b82f6;
}

.toast.warning {
  background: #fef3c7;
  border-left: 5px solid #f59e0b;
}

.toast.error {
  background: #fee2e2;
  border-left: 5px solid #ef4444;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  font-weight: bold;
  color: #444;
  cursor: pointer;
}

.toast-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 4px;
  width: 100%;
  background: rgba(0, 0, 0, 0.25);
  animation: shrink linear forwards;
}

@keyframes shrink {
  to {
    width: 0;
  }
}

@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 50%;
    transform: translateX(50%);
    width: calc(100vw - 24px);
    gap: 8px;
  }

  .toast {
    min-width: 0;
    width: 100%;
    padding: 12px 16px;
    font-size: 15px;
  }

  .close-btn {
    font-size: 18px;
  }
}
