.event-statistics-container {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Segoe UI', 'Roboto', sans-serif;
    z-index: 1000;
  }
  
  .event-statistics {
    background: #fff;
    border-radius: 24px;
    width: 98%;
    max-width: 950px;
    max-height: 92vh;
    overflow-y: auto;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
    position: relative;
    margin: 20px;
  }
  
  .header {
    padding: 36px 36px 28px 36px;
    border-bottom: 1px solid #f1f3f4;
    background: #f9fafb;
    border-radius: 24px 24px 0 0;
    position: relative;
  }
  
  .header h1 {
    margin: 0;
    font-size: 36px;
    font-weight: 600;
    color: #202124;
  }
  
  .close-btn {
    position: absolute;
    top: 24px;
    right: 24px;
    background: #f8f9fa;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 22px;
    color: #5f6368;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }
  
  .close-btn:hover {
    background: #e0e0e0;
    transform: scale(1.1);
  }
  
  .event-info {
    padding: 36px;
  }
  
  .event-title {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 28px;
  }
  
  .briefcase-icon {
    font-size: 28px;
    background: #f3e5f5;
    padding: 14px;
    border-radius: 14px;
  }
  
  .event-title h2 {
    font-size: 28px;
    font-weight: 600;
    color: #202124;
  }
  
  .event-details {
    margin-bottom: 28px;
  }
  
  .detail-item {
    display: flex;
    align-items: center;
    gap: 14px;
    font-size: 17px;
    color: #5f6368;
    margin-bottom: 12px;
  }
  
  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .tag {
    background: #f1f3f4;
    color: #5f6368;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 36px;
    background: #f6f8fa;
    align-items: center;
    justify-content: center;
  }
  
  .stat-card {
    padding: 32px 20px;
    border-radius: 18px;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }
  
  .stat-icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    margin-bottom: 14px;
  }
  
  .stat-icon {
    font-size: 30px;
    margin-left: 60px;
    margin-bottom: 20px;
  }
  
  .stat-value {
    font-size: 44px;
    font-weight: 700;
    margin-bottom: 10px;
  }
  
  .stat-label {
    font-size: 15px;
    color: #5f6368;
    font-weight: 500;
  }
  
  .stat-1 {
    background: rgba(74, 144, 226, 0.1);
  }
  
  .stat-2 {
    background: rgba(126, 211, 33, 0.1);
  }
  
  .stat-3 {
    background: rgba(208, 2, 27, 0.1);
  }
  
  .stat-4 {
    background: rgba(144, 19, 254, 0.1);
  }
  
  .rsvp-section {
    padding: 36px;
    background: #ffffff;
    border-top: 1px solid #f1f3f4;
  }
  
  .rsvp-section h3 {
    font-size: 22px;
    font-weight: 600;
    color: #202124;
    margin-bottom: 24px;
  }
  
  .rsvp-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  
  .rsvp-item {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .rsvp-status {
    min-width: 100px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .progress-container {
    flex: 1;
    height: 10px;
    background: #ececec;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .progress-bar {
    height: 100%;
    border-radius: 6px;
    transition: width 0.6s ease-in-out;
    position: relative;
  }
  
  .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
  }
  
  .rsvp-count {
    min-width: 50px;
    text-align: right;
    font-size: 18px;
    font-weight: 700;
  }
  
  .audience-section {
    padding: 36px;
    background: #f6f8fa;
    border-radius: 0 0 24px 24px;
  }
  
  .audience-section h3 {
    font-size: 22px;
    font-weight: 600;
    color: #202124;
    margin-bottom: 24px;
  }
  
  .department-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .department-item {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .department-name {
    min-width: 180px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .department-progress {
    background: #1a73e8;
    height: 10px;
    border-radius: 6px;
    flex: 1;
  }
  
  .department-count {
    font-size: 18px;
    font-weight: 700;
    color: #1a73e8;
    min-width: 50px;
    text-align: right;
  }
  
  .event-statistics::-webkit-scrollbar {
    width: 6px;
  }
  
  .event-statistics::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 3px;
  }
  
  .event-statistics::-webkit-scrollbar-thumb:hover {
    background: #bdc1c6;
  }
  