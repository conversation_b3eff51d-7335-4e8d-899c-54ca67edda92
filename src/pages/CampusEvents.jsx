// CampusEvents.jsx
import React, { useState, useEffect } from "react";
import { TrendingUp, Calendar, Star, Heart } from "lucide-react";
import { useAuth } from "../hook/useAuth";
import "./CampusEvents.css";

const CampusEvents = () => {
  const { user, getUserRole, isStudent, isClub, supabase } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedEventTypes, setSelectedEventTypes] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [userResponses, setUserResponses] = useState({});
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({ total: 0, upcoming: 0, past: 0 });

  // Get user role from auth context
  const userRole = getUserRole();

  // Event types for filtering
  const eventTypes = [
    "Hackathons",
    "Workshops",
    "Competitions",
    "Seminars",
    "Festivals",
    "Cultural Shows",
  ];

  // Categories for filtering
  const categories = [
    "E-Cell",
    "Arts (Dance)",
    "Arts (Drama)",
    "Arts (Music)",
    "Sports",
    "Content Creation",
    "Dev Club",
    "Photography",
    "Debate Society",
  ];

  // Create mock events for fallback
  const createMockEvents = () => {
    const mockEvents = [
      {
        id: 'mock-1',
        title: 'Inter-Department Basketball Tournament',
        description: 'Annual basketball championship between all departments. Register your team now and compete for the golden trophy.',
        event_date: '2025-06-25',
        event_time: '17:00:00',
        venue: 'Sports Complex Court 1',
        event_type: 'optional',
        status: 'past',
        attendees_count: 234,
        needs_volunteers: false,
        tags: ['sports'],
        club: { name: 'Sports Club' }
      },
      {
        id: 'mock-2',
        title: 'AI Club Hackathon',
        description: 'Join us for a 24-hour coding marathon focused on AI and machine learning projects.',
        event_date: '2025-07-20',
        event_time: '10:00:00',
        venue: 'Seminar Hall A2',
        event_type: 'optional',
        status: 'upcoming',
        attendees_count: 127,
        needs_volunteers: true,
        tags: ['technical'],
        club: { name: 'Dev Club' }
      },
      {
        id: 'mock-3',
        title: 'Photography Workshop',
        description: 'Learn the basics of digital photography and advanced techniques from professional photographers.',
        event_date: '2025-07-25',
        event_time: '14:00:00',
        venue: 'Creative Studio B',
        event_type: 'optional',
        status: 'upcoming',
        attendees_count: 45,
        needs_volunteers: true,
        tags: ['workshop'],
        club: { name: 'Photography Club' }
      }
    ];

    return mockEvents.map(transformEvent);
  };

  // Transform database event to component format
  const transformEvent = (dbEvent) => {
    if (!dbEvent) {
      console.warn('Null event passed to transformEvent');
      return null;
    }

    console.log('Transforming event:', dbEvent);

    // Determine status based on date
    const today = new Date();
    const eventDate = new Date(dbEvent.event_date);
    const status = dbEvent.status || (eventDate < today ? 'past' : 'upcoming');

    // Format date and time
    const formattedDate = eventDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formattedTime = dbEvent.event_time ?
      new Date(`2000-01-01T${dbEvent.event_time}`).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      }) : 'Time TBD';

    // Get category from tags or default
    const category = dbEvent.tags && dbEvent.tags.length > 0 ?
      dbEvent.tags[0] :
      (dbEvent.club?.name || 'General');

    // Map event_type to eventType
    const eventTypeMap = {
      'optional': 'Workshops',
      'compulsory': 'Seminars',
      'batch_compulsory': 'Competitions'
    };

    // Get emoji for category
    const getEventEmoji = (cat) => {
      const emojiMap = {
        'Sports': '🏀', 'Tech': '💻', 'Arts (Drama)': '🎭',
        'Arts (Music)': '🎵', 'Arts (Dance)': '💃', 'Photography': '📸',
        'Dev Club': '⚡', 'E-Cell': '💼', 'Content Creation': '📝',
        'Debate Society': '🗣️', 'workshop': '🔧', 'seminar': '📚',
        'social': '🎉', 'sports': '🏀', 'cultural': '🎪',
        'technical': '💻', 'academic': '📖', 'networking': '🤝',
        'competition': '🏆', 'meeting': '📋', 'conference': '🎤',
        'party': '🎊'
      };
      return emojiMap[cat] || '📅';
    };

    const transformedEvent = {
      id: dbEvent.id,
      name: dbEvent.title || 'Untitled Event',
      description: dbEvent.description || 'No description available',
      date: formattedDate,
      time: formattedTime,
      rawDate: dbEvent.event_date,
      venue: dbEvent.venue || 'Venue TBD',
      club: dbEvent.club?.name || 'Unknown Club',
      category: category,
      eventType: eventTypeMap[dbEvent.event_type] || 'General',
      attendees: dbEvent.attendees_count || 0,
      status: status,
      needsVolunteers: dbEvent.needs_volunteers || false,
      maxVolunteers: dbEvent.max_volunteers || 0,
      imagePlaceholder: getEventEmoji(category),
      hasImage: !!dbEvent.poster_url,
      imageUrl: dbEvent.poster_url,
      tags: dbEvent.tags || [],
      rsvpLimit: dbEvent.rsvp_limit,
      targetBatchYear: dbEvent.target_batch_year,
      createdAt: dbEvent.created_at,
      updatedAt: dbEvent.updated_at
    };

    console.log('Transformed event result:', transformedEvent);
    return transformedEvent;
  };

  // Load events on component mount
  useEffect(() => {
    const loadData = async () => {
      console.log('Loading events data...');
      console.log('Supabase client:', supabase);
      console.log('User:', user);

      if (!supabase) {
        console.error('Supabase client not available');
        setError('Database connection not available');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // For now, let's use mock data since database queries are hanging
        console.log('🔄 Using mock data (database queries are hanging)');
        console.log('📝 Database issue: Queries to events table are timing out');
        console.log('💡 Possible solutions:');
        console.log('   1. Check if events table has the correct structure');
        console.log('   2. Verify RLS policies (you said RLS is disabled, so this should be fine)');
        console.log('   3. Check if there are any database triggers causing issues');
        console.log('   4. Try running a simple query directly in Supabase SQL editor');

        setEvents(createMockEvents());
        setStats({ total: 3, upcoming: 2, past: 1 });
        return;

        // TODO: Uncomment this section once database issues are resolved
        /*
        console.log('Testing basic Supabase connectivity...');

        const { data: simpleData, error: simpleError } = await supabase
          .from('events')
          .select('*')
          .limit(5);

        console.log('Simple query result - data:', simpleData);
        console.log('Simple query result - error:', simpleError);

        if (simpleError) {
          console.error('Simple query failed:', simpleError);
          throw simpleError;
        }

        if (!simpleData || simpleData.length === 0) {
          console.log('✅ Events table exists but is empty.');
          const sampleEvent = {
            id: 'sample-1',
            title: 'Sample Event (No events in database)',
            description: 'Create some events to see real data here.',
            event_date: new Date().toISOString().split('T')[0],
            event_time: '18:00:00',
            venue: 'Sample Venue',
            event_type: 'optional',
            status: 'upcoming',
            attendees_count: 0,
            needs_volunteers: false,
            max_volunteers: 0,
            tags: ['sample'],
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            club: { name: 'Sample Club' }
          };

          const sampleEvents = [sampleEvent];
          const transformedSampleEvents = sampleEvents.map(transformEvent);
          setEvents(transformedSampleEvents);
          setStats({ total: 1, upcoming: 1, past: 0 });
          return;
        }

        console.log(`✅ Found ${simpleData.length} events in database:`, simpleData);

        const transformedEvents = simpleData.map(transformEvent);
        setEvents(transformedEvents);

        const today = new Date().toISOString().split('T')[0];
        const upcoming = transformedEvents.filter(event =>
          event.status === 'upcoming' || new Date(event.rawDate) >= new Date(today)
        ).length;
        const past = transformedEvents.filter(event =>
          event.status === 'past' || new Date(event.rawDate) < new Date(today)
        ).length;

        setStats({
          total: transformedEvents.length,
          upcoming,
          past
        });

        console.log('✅ Events loaded successfully:', transformedEvents);
        */



        // Approach 1: Try with club information and is_active filter
        console.log('Trying to fetch events with club data and is_active filter...');
        const result1 = await supabase
          .from('events')
          .select(`
            *,
            club:club_id (
              id,
              name,
              description
            )
          `)
          .eq('is_active', true)
          .order('event_date', { ascending: true });

        console.log('Approach 1 result:', result1);

        if (!result1.error && result1.data) {
          eventsData = result1.data;
        } else {
          // Approach 2: Try without club data but with is_active filter
          console.log('Approach 1 failed, trying without club data...');
          const result2 = await supabase
            .from('events')
            .select('*')
            .eq('is_active', true)
            .order('event_date', { ascending: true });

          console.log('Approach 2 result:', result2);

          if (!result2.error && result2.data) {
            eventsData = result2.data;
          } else {
            // Approach 3: Try without any filters
            console.log('Approach 2 failed, trying without any filters...');
            const result3 = await supabase
              .from('events')
              .select('*')
              .order('event_date', { ascending: true });

            console.log('Approach 3 result:', result3);

            if (!result3.error && result3.data) {
              eventsData = result3.data;
            } else {
              // Approach 4: Just get all events without ordering
              console.log('Approach 3 failed, trying simplest query...');
              const result4 = await supabase
                .from('events')
                .select('*');

              console.log('Approach 4 result:', result4);

              if (result4.error) {
                eventsError = result4.error;
              } else {
                eventsData = result4.data;
              }
            }
          }
        }

        if (eventsError) {
          console.error('All approaches failed. Final error:', eventsError);
          throw eventsError;
        }

        console.log('Fetched events data:', eventsData);

        if (!eventsData || eventsData.length === 0) {
          console.log('No events found in database');

          // For debugging: Create a sample event to test the UI
          const sampleEvent = {
            id: 'sample-1',
            title: 'Sample Event (No events in database)',
            description: 'This is a sample event shown because no events were found in your database. Create some events to see real data here.',
            event_date: new Date().toISOString().split('T')[0],
            event_time: '18:00:00',
            venue: 'Sample Venue',
            event_type: 'optional',
            status: 'upcoming',
            attendees_count: 0,
            needs_volunteers: false,
            max_volunteers: 0,
            tags: ['sample'],
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            club: { name: 'Sample Club' }
          };

          const sampleEvents = [sampleEvent];
          const transformedSampleEvents = sampleEvents.map(transformEvent);
          setEvents(transformedSampleEvents);
          setStats({ total: 1, upcoming: 1, past: 0 });
          return;
        }

        // Transform events to component format
        const transformedEvents = eventsData.map(transformEvent);
        console.log('Transformed events:', transformedEvents);
        setEvents(transformedEvents);

        // Calculate stats
        const today = new Date().toISOString().split('T')[0];
        const upcoming = transformedEvents.filter(event =>
          event.status === 'upcoming' || new Date(event.rawDate) >= new Date(today)
        ).length;
        const past = transformedEvents.filter(event =>
          event.status === 'past' || new Date(event.rawDate) < new Date(today)
        ).length;

        setStats({
          total: transformedEvents.length,
          upcoming,
          past
        });

      } catch (err) {
        console.error('Error loading events:', err);
        setError('Failed to load events. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [supabase, user]);

  // Load user responses when events are loaded
  useEffect(() => {
    const loadResponses = async () => {
      if (!user || !supabase || events.length === 0) return;

      try {
        // Fetch user responses for all events
        const { data: responses, error } = await supabase
          .from('user_event_responses')
          .select('event_id, response_type')
          .eq('user_id', user.id);

        if (error) {
          console.error('Error loading user responses:', error);
          return;
        }

        // Convert to object format
        const responsesObj = {};
        responses.forEach(response => {
          responsesObj[response.event_id] = response.response_type.replace('_', '-');
        });

        setUserResponses(responsesObj);
      } catch (err) {
        console.error('Error loading user responses:', err);
      }
    };

    loadResponses();
  }, [user, events.length, supabase]);

  // Function to reload events data (used in error retry)
  const loadEventsData = async () => {
    if (!supabase) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch events with club information
      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select(`
          *,
          club:club_id (
            id,
            name,
            description
          )
        `)
        .eq('is_active', true)
        .order('event_date', { ascending: true });

      if (eventsError) {
        throw eventsError;
      }

      // Transform events to component format
      const transformedEvents = eventsData.map(transformEvent);
      setEvents(transformedEvents);

      // Calculate stats
      const today = new Date().toISOString().split('T')[0];
      const upcoming = transformedEvents.filter(event =>
        event.status === 'upcoming' || new Date(event.rawDate) >= new Date(today)
      ).length;
      const past = transformedEvents.filter(event =>
        event.status === 'past' || new Date(event.rawDate) < new Date(today)
      ).length;

      setStats({
        total: transformedEvents.length,
        upcoming,
        past
      });

    } catch (err) {
      console.error('Error loading events:', err);
      setError('Failed to load events. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleUserResponse = async (eventId, response) => {
    if (!user || !supabase) return;

    try {
      const responseType = response.replace('-', '_');

      // Check if response already exists
      const { data: existingResponse } = await supabase
        .from('user_event_responses')
        .select('*')
        .eq('user_id', user.id)
        .eq('event_id', eventId)
        .single();

      if (existingResponse) {
        // Update existing response
        const { error } = await supabase
          .from('user_event_responses')
          .update({
            response_type: responseType,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('event_id', eventId);

        if (error) throw error;
      } else {
        // Create new response
        const { error } = await supabase
          .from('user_event_responses')
          .insert([{
            user_id: user.id,
            event_id: eventId,
            response_type: responseType,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }]);

        if (error) throw error;
      }

      // Update local state
      setUserResponses(prev => ({
        ...prev,
        [eventId]: response
      }));

      // Update attendees count if response is 'going'
      if (response === 'going') {
        // If previous response was not 'going', increment count
        if (!existingResponse || existingResponse.response_type !== 'going') {
          await updateEventAttendeesCount(eventId, 1);
        }
      } else {
        // If previous response was 'going' and new response is not, decrement count
        if (existingResponse && existingResponse.response_type === 'going') {
          await updateEventAttendeesCount(eventId, -1);
        }
      }

    } catch (err) {
      console.error('Error updating user response:', err);
    }
  };

  const updateEventAttendeesCount = async (eventId, increment) => {
    try {
      // Get current count
      const { data: currentEvent } = await supabase
        .from('events')
        .select('attendees_count')
        .eq('id', eventId)
        .single();

      const newCount = Math.max(0, (currentEvent.attendees_count || 0) + increment);

      await supabase
        .from('events')
        .update({
          attendees_count: newCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', eventId);

      // Update local events state
      setEvents(prev => prev.map(event =>
        event.id === eventId
          ? { ...event, attendees: newCount }
          : event
      ));

    } catch (err) {
      console.error('Error updating attendees count:', err);
    }
  };

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const handleEventTypeChange = (eventType) => {
    setSelectedEventTypes((prev) =>
      prev.includes(eventType)
        ? prev.filter((e) => e !== eventType)
        : [...prev, eventType]
    );
  };



  const clearFilters = () => {
    setSelectedCategories([]);
    setSelectedEventTypes([]);
    setSearchQuery("");
  };

  const filteredEvents = events.filter((event) => {
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "upcoming" && event.status === "upcoming") ||
      (activeTab === "past" && event.status === "past");

    const matchesCategory =
      selectedCategories.length === 0 ||
      selectedCategories.includes(event.category);
    const matchesEventType =
      selectedEventTypes.length === 0 ||
      selectedEventTypes.includes(event.eventType);
    const matchesSearch =
      searchQuery === "" ||
      event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesCategory && matchesEventType && matchesSearch;
  });

  // Get user's RSVPs count
  const getUserRSVPCount = () => {
    return Object.values(userResponses).filter(
      (response) => response === "going"
    ).length;
  };



  const EventCard = ({ event }) => {
    const userResponse = userResponses[event.id];

    return (
      <div className="event-card">
        <div className="event-image">
          {event.hasImage ? (
            <img src={event.imageUrl} alt={event.name} />
          ) : (
            <div className="event-placeholder-overlay">
              <div className="event-placeholder">{event.imagePlaceholder}</div>
            </div>
          )}

          {event.status === "past" && (
            <div className="event-status-badge">Past Event</div>
          )}
          <div className="photo-count">
            {event.hasImage ? "1 photo" : "No photos"}
          </div>
        </div>

        <div className="event-content">
          <div className="event-header">
            <h3 className="event-title">{event.name}</h3>
            <span
              className={`category-badge ${event.category
                .toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[()]/g, "")}`}
            >
              {event.category}
            </span>
          </div>

          <p className="event-description">{event.description}</p>

          <div className="event-details">
            <div className="event-detail">
              <span className="detail-icon">📅</span>
              <span>
                {event.date} • {event.time}
              </span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">📍</span>
              <span>{event.venue}</span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">👥</span>
              <span>{event.attendees} interested</span>
            </div>
          </div>

          <div className="event-tags">
            <span className="tag">{event.category}</span>
            <span className="tag">{event.eventType}</span>
            <span className="tag">{event.club}</span>
          </div>

          {isStudent() ? (
            <div className="student-actions">
              <button
                className={`action-btn going ${
                  userResponse === "going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "going")}
              >
                ✓ Going
              </button>
              <button
                className={`action-btn not-going ${
                  userResponse === "not-going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "not-going")}
              >
                ✗ Not Going
              </button>
              <button
                className={`action-btn maybe ${
                  userResponse === "maybe" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "maybe")}
              >
                ? Maybe
              </button>
              {event.needsVolunteers && (
                <button
                  className={`action-btn volunteer ${
                    userResponse === "volunteer" ? "active" : ""
                  }`}
                  onClick={() => handleUserResponse(event.id, "volunteer")}
                >
                  🙋‍♂️ Volunteer
                </button>
              )}
            </div>
          ) : (
            <div className="club-actions">
              <button className="action-btn stats">📊 Stats</button>
              <button className="action-btn edit">✏️ Edit</button>
              <button className="action-btn delete">🗑️ Delete</button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Show loading if user data is not yet available
  if (!user) {
    return (
      <div className="campus-events">
        <div className="loading-container">
          <p>Loading user data...</p>
        </div>
      </div>
    );
  }

  // Show loading while fetching events
  if (loading) {
    return (
      <div className="campus-events">
        <div className="loading-container">
          <p>Loading events...</p>
        </div>
      </div>
    );
  }

  // Show error if there's an error
  if (error) {
    return (
      <div className="campus-events">
        <div className="error-container">
          <p>Error: {error}</p>
          <button onClick={loadEventsData} className="retry-btn">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="campus-events">
      {/* Debug Info - Remove this in production */}
      <div style={{
        background: '#f0f0f0',
        padding: '10px',
        margin: '10px 0',
        borderRadius: '5px',
        fontSize: '12px'
      }}>
        <strong>Debug Info:</strong><br/>
        Supabase: {supabase ? '✅ Connected' : '❌ Not connected'}<br/>
        User: {user ? `✅ ${user.email}` : '❌ Not logged in'}<br/>
        Events loaded: {events.length}<br/>
        Loading: {loading ? 'Yes' : 'No'}<br/>
        Error: {error || 'None'}
      </div>

      {/* Header */}
      <div className="header">
        <div className="header-left">
          <div className="app-icon">📅</div>
          <div>
            <h1 className="app-title">Campus Events</h1>
            <p className="app-subtitle">
              ✨ Discover amazing events happening on campus
            </p>
          </div>
        </div>
        <div className="header-right">
          <div className="user-role-display">
            <span className="role-label">
              {isStudent() ? "Student" : isClub() ? "Club" : "User"} View
            </span>
            <span className="role-badge">
              {isStudent() ? "🎓" : isClub() ? "🏛️" : "👤"}
            </span>
          </div>
          <button
            className="filter-toggle"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </button>
        </div>
      </div>

      {/* Floating Quick Stats */}
      <div className="floating-quick-stats">
        <div className="stats-header">
          <TrendingUp className="stats-icon" />
          <span className="stats-title">Quick Stats</span>
        </div>
        <div className="stats-list">
          <div className="stat-item">
            <Calendar className="stat-icon" />
            <span className="stat-label">Total Events</span>
            <span className="stat-number">{stats.total}</span>
          </div>
          <div className="stat-item">
            <Star className="stat-icon" />
            <span className="stat-label">
              {isStudent() ? "My RSVPs" : "My Events"}
            </span>
            <span className="stat-number">
              {isStudent() ? getUserRSVPCount() : "0"}
            </span>
          </div>
          <div className="stat-item">
            <Heart className="stat-icon" />
            <span className="stat-label">Upcoming</span>
            <span className="stat-number">{stats.upcoming}</span>
          </div>
        </div>
      </div>

      <div className="main-content">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="filters-sidebar">
            <div className="filter-section">
              <h3>Search Events</h3>
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-section">
              <h3>Date Range</h3>
              <input
                type="date"
                className="date-input"
                placeholder="Pick a date"
              />
            </div>

            <div className="filter-section">
              <h3>Club Categories</h3>
              {categories.map((category) => (
                <label key={category} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => handleCategoryChange(category)}
                  />
                  {category}
                </label>
              ))}
            </div>

            <div className="filter-section">
              <h3>Event Types</h3>
              {eventTypes.map((eventType) => (
                <label key={eventType} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedEventTypes.includes(eventType)}
                    onChange={() => handleEventTypeChange(eventType)}
                  />
                  {eventType}
                </label>
              ))}
            </div>

            <button className="clear-filters-btn" onClick={clearFilters}>
              Clear All Filters
            </button>
          </div>
        )}

        {/* Events Content */}
        <div className="events-content">
          {/* Tabs */}
          <div className="content-tabs">
            <button
              className={`content-tab ${activeTab === "all" ? "active" : ""}`}
              onClick={() => setActiveTab("all")}
            >
              All Events
            </button>
            <button
              className={`content-tab ${
                activeTab === "upcoming" ? "active" : ""
              }`}
              onClick={() => setActiveTab("upcoming")}
            >
              Upcoming
            </button>
            <button
              className={`content-tab ${activeTab === "past" ? "active" : ""}`}
              onClick={() => setActiveTab("past")}
            >
              Past
            </button>
          </div>

          {/* Events Grid */}
          <div className="events-grid">
            {filteredEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <div className="no-events">
              <p>No events found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CampusEvents;
