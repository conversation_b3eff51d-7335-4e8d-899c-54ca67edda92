// CampusEvents.jsx
import React, { useState, useEffect } from "react";
import { TrendingUp, Calendar, Star, Heart } from "lucide-react";
import { useAuth } from "../hook/useAuth";
import "./CampusEvents.css";

const CampusEvents = () => {
  const { user, getUserRole, isStudent, isClub } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedEventTypes, setSelectedEventTypes] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [userResponses, setUserResponses] = useState({});

  // Get user role from auth context
  const userRole = getUserRole();

  // Event types for filtering
  const eventTypes = [
    "Hackathons",
    "Workshops",
    "Competitions",
    "Seminars",
    "Festivals",
    "Cultural Shows",
  ];

  // Load events and user responses on component mount
  useEffect(() => {
    loadEventsData();
    loadStats();
    loadCategories();
  }, []);

  const loadEventsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const eventsData = await fetchEvents();
      const transformedEvents = eventsData.map(transformEventForComponent);
      setEvents(transformedEvents);
    } catch (err) {
      console.error('Error loading events:', err);
      setError('Failed to load events. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getEventsStats();
      setStats(statsData);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await getEventCategories();
      setCategories(categoriesData);
    } catch (err) {
      console.error('Error loading categories:', err);
      // Use default categories if fetch fails
      setCategories([
        "E-Cell",
        "Arts (Dance)",
        "Arts (Drama)",
        "Arts (Music)",
        "Sports",
        "Content Creation",
        "Dev Club",
        "Photography",
        "Debate Society",
      ]);
    }
  };

  const loadUserResponses = async (eventsList) => {
    if (!user || !supabase || !eventsList || eventsList.length === 0) return;

    try {
      const responses = {};
      for (const event of eventsList) {
        const response = await getUserEventResponse(user.id, event.id);
        if (response) {
          responses[event.id] = response.response_type.replace('_', '-');
        }
      }
      setUserResponses(responses);
    } catch (err) {
      console.error('Error loading user responses:', err);
    }
  };

  // Load user responses when user changes or events are loaded
  useEffect(() => {
    if (user && events.length > 0) {
      loadUserResponses(events);
    }
  }, [user, events.length]); // Use events.length instead of events to avoid infinite loop

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const handleEventTypeChange = (eventType) => {
    setSelectedEventTypes((prev) =>
      prev.includes(eventType)
        ? prev.filter((e) => e !== eventType)
        : [...prev, eventType]
    );
  };

  const handleUserResponse = (eventId, response) => {
    setUserResponses((prev) => ({
      ...prev,
      [eventId]: response,
    }));
  };

  const clearFilters = () => {
    setSelectedCategories([]);
    setSelectedEventTypes([]);
    setSearchQuery("");
  };

  const filteredEvents = events.filter((event) => {
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "upcoming" && event.status === "upcoming") ||
      (activeTab === "past" && event.status === "past");

    const matchesCategory =
      selectedCategories.length === 0 ||
      selectedCategories.includes(event.category);
    const matchesEventType =
      selectedEventTypes.length === 0 ||
      selectedEventTypes.includes(event.eventType);
    const matchesSearch =
      searchQuery === "" ||
      event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesCategory && matchesEventType && matchesSearch;
  });

  // Get user's RSVPs count
  const getUserRSVPCount = () => {
    return Object.values(userResponses).filter(
      (response) => response === "going"
    ).length;
  };

  // Get upcoming events count
  const getUpcomingEventsCount = () => {
    return events.filter((event) => event.status === "upcoming").length;
  };

  const EventCard = ({ event }) => {
    const userResponse = userResponses[event.id];

    return (
      <div className="event-card">
        <div className="event-image">
          {event.hasImage ? (
            <img src={event.imageUrl} alt={event.name} />
          ) : (
            <div className="event-placeholder-overlay">
              <div className="event-placeholder">{event.imagePlaceholder}</div>
            </div>
          )}

          {event.status === "past" && (
            <div className="event-status-badge">Past Event</div>
          )}
          <div className="photo-count">
            {event.hasImage ? "1 photo" : "No photos"}
          </div>
        </div>

        <div className="event-content">
          <div className="event-header">
            <h3 className="event-title">{event.name}</h3>
            <span
              className={`category-badge ${event.category
                .toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[()]/g, "")}`}
            >
              {event.category}
            </span>
          </div>

          <p className="event-description">{event.description}</p>

          <div className="event-details">
            <div className="event-detail">
              <span className="detail-icon">📅</span>
              <span>
                {event.date} • {event.time}
              </span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">📍</span>
              <span>{event.venue}</span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">👥</span>
              <span>{event.attendees} interested</span>
            </div>
          </div>

          <div className="event-tags">
            <span className="tag">{event.category}</span>
            <span className="tag">{event.eventType}</span>
            <span className="tag">{event.club}</span>
          </div>

          {isStudent() ? (
            <div className="student-actions">
              <button
                className={`action-btn going ${
                  userResponse === "going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "going")}
              >
                ✓ Going
              </button>
              <button
                className={`action-btn not-going ${
                  userResponse === "not-going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "not-going")}
              >
                ✗ Not Going
              </button>
              <button
                className={`action-btn maybe ${
                  userResponse === "maybe" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "maybe")}
              >
                ? Maybe
              </button>
              {event.needsVolunteers && (
                <button
                  className={`action-btn volunteer ${
                    userResponse === "volunteer" ? "active" : ""
                  }`}
                  onClick={() => handleUserResponse(event.id, "volunteer")}
                >
                  🙋‍♂️ Volunteer
                </button>
              )}
            </div>
          ) : (
            <div className="club-actions">
              <button className="action-btn stats">📊 Stats</button>
              <button className="action-btn edit">✏️ Edit</button>
              <button className="action-btn delete">🗑️ Delete</button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Show loading if user data is not yet available
  if (!user) {
    return (
      <div className="campus-events">
        <div className="loading-container">
          <p>Loading user data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="campus-events">
      {/* Header */}
      <div className="header">
        <div className="header-left">
          <div className="app-icon">📅</div>
          <div>
            <h1 className="app-title">Campus Events</h1>
            <p className="app-subtitle">
              ✨ Discover amazing events happening on campus
            </p>
          </div>
        </div>
        <div className="header-right">
          <div className="user-role-display">
            <span className="role-label">
              {isStudent() ? "Student" : isClub() ? "Club" : "User"} View
            </span>
            <span className="role-badge">
              {isStudent() ? "🎓" : isClub() ? "🏛️" : "👤"}
            </span>
          </div>
          <button
            className="filter-toggle"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </button>
        </div>
      </div>

      {/* Floating Quick Stats */}
      <div className="floating-quick-stats">
        <div className="stats-header">
          <TrendingUp className="stats-icon" />
          <span className="stats-title">Quick Stats</span>
        </div>
        <div className="stats-list">
          <div className="stat-item">
            <Calendar className="stat-icon" />
            <span className="stat-label">Total Events</span>
            <span className="stat-number">{events.length}</span>
          </div>
          <div className="stat-item">
            <Star className="stat-icon" />
            <span className="stat-label">
              {isStudent() ? "My RSVPs" : "My Events"}
            </span>
            <span className="stat-number">
              {isStudent() ? getUserRSVPCount() : "0"}
            </span>
          </div>
          <div className="stat-item">
            <Heart className="stat-icon" />
            <span className="stat-label">Upcoming</span>
            <span className="stat-number">{getUpcomingEventsCount()}</span>
          </div>
        </div>
      </div>

      <div className="main-content">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="filters-sidebar">
            <div className="filter-section">
              <h3>Search Events</h3>
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-section">
              <h3>Date Range</h3>
              <input
                type="date"
                className="date-input"
                placeholder="Pick a date"
              />
            </div>

            <div className="filter-section">
              <h3>Club Categories</h3>
              {categories.map((category) => (
                <label key={category} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => handleCategoryChange(category)}
                  />
                  {category}
                </label>
              ))}
            </div>

            <div className="filter-section">
              <h3>Event Types</h3>
              {eventTypes.map((eventType) => (
                <label key={eventType} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedEventTypes.includes(eventType)}
                    onChange={() => handleEventTypeChange(eventType)}
                  />
                  {eventType}
                </label>
              ))}
            </div>

            <button className="clear-filters-btn" onClick={clearFilters}>
              Clear All Filters
            </button>
          </div>
        )}

        {/* Events Content */}
        <div className="events-content">
          {/* Tabs */}
          <div className="content-tabs">
            <button
              className={`content-tab ${activeTab === "all" ? "active" : ""}`}
              onClick={() => setActiveTab("all")}
            >
              All Events
            </button>
            <button
              className={`content-tab ${
                activeTab === "upcoming" ? "active" : ""
              }`}
              onClick={() => setActiveTab("upcoming")}
            >
              Upcoming
            </button>
            <button
              className={`content-tab ${activeTab === "past" ? "active" : ""}`}
              onClick={() => setActiveTab("past")}
            >
              Past
            </button>
          </div>

          {/* Events Grid */}
          <div className="events-grid">
            {filteredEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <div className="no-events">
              <p>No events found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CampusEvents;
