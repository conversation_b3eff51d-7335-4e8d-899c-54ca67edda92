// CampusEvents.jsx
import React, { useState, useEffect } from "react";
import { TrendingUp, Calendar, Star, Heart } from "lucide-react";
import { useAuth } from "../hook/useAuth";
import "./CampusEvents.css";

const CampusEvents = () => {
  const { user, getUserRole, isStudent, isClub } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedEventTypes, setSelectedEventTypes] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [userResponses, setUserResponses] = useState({});
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({ total: 0, upcoming: 0, past: 0 });

  // Get user role from auth context
  const userRole = getUserRole();

  // Direct fetch function using Supabase REST API
  const fetchEventsDirectly = async () => {
    const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
    const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/events?select=*&limit=10`, {
        method: 'GET',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      console.error('Failed to fetch events:', error);
      throw error;
    }
  };

  // Transform database event to component format
  const transformEvent = (dbEvent) => {
    if (!dbEvent) return null;

    // Determine status based on date
    const today = new Date();
    const eventDate = new Date(dbEvent.event_date);
    const status = dbEvent.status || (eventDate < today ? 'past' : 'upcoming');

    // Format date and time
    const formattedDate = eventDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formattedTime = dbEvent.event_time ?
      new Date(`2000-01-01T${dbEvent.event_time}`).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      }) : 'Time TBD';

    // Get category from tags or default
    const category = dbEvent.tags && dbEvent.tags.length > 0 ?
      dbEvent.tags[0] : 'General';

    // Get emoji for category
    const getEventEmoji = (cat) => {
      const emojiMap = {
        'Sports': '🏀', 'Tech': '💻', 'Arts (Drama)': '🎭',
        'Arts (Music)': '🎵', 'Arts (Dance)': '💃', 'Photography': '📸',
        'Dev Club': '⚡', 'E-Cell': '💼', 'Content Creation': '📝',
        'Debate Society': '🗣️', 'workshop': '🔧', 'seminar': '📚',
        'social': '🎉', 'sports': '🏀', 'cultural': '🎪',
        'technical': '💻', 'academic': '📖', 'networking': '🤝',
        'competition': '🏆', 'meeting': '📋', 'conference': '🎤',
        'party': '🎊'
      };
      return emojiMap[cat] || '📅';
    };

    return {
      id: dbEvent.id,
      name: dbEvent.title || 'Untitled Event',
      description: dbEvent.description || 'No description available',
      date: formattedDate,
      time: formattedTime,
      rawDate: dbEvent.event_date,
      venue: dbEvent.venue || 'Venue TBD',
      club: 'Event Club',
      category: category,
      eventType: 'General',
      attendees: dbEvent.attendees_count || 0,
      status: status,
      needsVolunteers: dbEvent.needs_volunteers || false,
      maxVolunteers: dbEvent.max_volunteers || 0,
      imagePlaceholder: getEventEmoji(category),
      hasImage: !!dbEvent.poster_url,
      imageUrl: dbEvent.poster_url,
      tags: dbEvent.tags || [],
      rsvpLimit: dbEvent.rsvp_limit,
      targetBatchYear: dbEvent.target_batch_year,
      createdAt: dbEvent.created_at,
      updatedAt: dbEvent.updated_at
    };
  };

  // Load events on component mount
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use direct fetch to get events
        const eventsData = await fetchEventsDirectly();

        if (eventsData && eventsData.length > 0) {
          // Transform events to component format
          const transformedEvents = eventsData.map(transformEvent);
          setEvents(transformedEvents);

          // Calculate stats
          const today = new Date().toISOString().split('T')[0];
          const upcoming = transformedEvents.filter(event =>
            event.status === 'upcoming' || new Date(event.rawDate) >= new Date(today)
          ).length;
          const past = transformedEvents.filter(event =>
            event.status === 'past' || new Date(event.rawDate) < new Date(today)
          ).length;

          setStats({
            total: transformedEvents.length,
            upcoming,
            past
          });

        } else {
          // Show sample event if no real events exist
          const sampleEvents = [{
            id: 'sample-1',
            name: 'Sample Event (No events in database)',
            description: 'This is a sample event shown because no events were found in your database. Create some events to see real data here.',
            date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
            time: '6:00 PM - 8:00 PM',
            venue: 'Sample Venue',
            club: 'Sample Club',
            category: 'General',
            eventType: 'Workshop',
            attendees: 0,
            status: 'upcoming',
            needsVolunteers: false,
            imagePlaceholder: '📅',
            hasImage: false,
          }];

          setEvents(sampleEvents);
          setStats({ total: 1, upcoming: 1, past: 0 });
        }

      } catch (error) {
        console.error('❌ Failed to load events:', error);
        setError('Failed to load events. Please try again later.');

        // Show fallback mock data
        const mockEvents = [
          {
            id: 'mock-1',
            name: 'Basketball Tournament (Mock Data)',
            description: 'This is mock data shown due to database connection issues.',
            date: 'June 25, 2025',
            time: '5:00 PM - 8:00 PM',
            venue: 'Sports Complex',
            club: 'Sports Club',
            category: 'Sports',
            eventType: 'Competition',
            attendees: 234,
            status: 'past',
            needsVolunteers: false,
            imagePlaceholder: '�',
            hasImage: false,
          }
        ];

        setEvents(mockEvents);
        setStats({ total: 1, upcoming: 0, past: 1 });

      } finally {
        setLoading(false);
      }
    };

    loadEvents();
  }, []);

  const categories = [
    "E-Cell",
    "Arts (Dance)",
    "Arts (Drama)",
    "Arts (Music)",
    "Sports",
    "Content Creation",
    "Dev Club",
    "Photography",
    "Debate Society",
  ];
  const eventTypes = [
    "Hackathons",
    "Workshops",
    "Competitions",
    "Seminars",
    "Festivals",
    "Cultural Shows",
  ];

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const handleEventTypeChange = (eventType) => {
    setSelectedEventTypes((prev) =>
      prev.includes(eventType)
        ? prev.filter((e) => e !== eventType)
        : [...prev, eventType]
    );
  };

  const handleUserResponse = (eventId, response) => {
    setUserResponses((prev) => ({
      ...prev,
      [eventId]: response,
    }));
  };

  const clearFilters = () => {
    setSelectedCategories([]);
    setSelectedEventTypes([]);
    setSearchQuery("");
  };

  const filteredEvents = events.filter((event) => {
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "upcoming" && event.status === "upcoming") ||
      (activeTab === "past" && event.status === "past");

    const matchesCategory =
      selectedCategories.length === 0 ||
      selectedCategories.includes(event.category);
    const matchesEventType =
      selectedEventTypes.length === 0 ||
      selectedEventTypes.includes(event.eventType);
    const matchesSearch =
      searchQuery === "" ||
      event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesCategory && matchesEventType && matchesSearch;
  });

  // Get user's RSVPs count
  const getUserRSVPCount = () => {
    return Object.values(userResponses).filter(
      (response) => response === "going"
    ).length;
  };

  // Get upcoming events count
  const getUpcomingEventsCount = () => {
    return events.filter((event) => event.status === "upcoming").length;
  };

  const EventCard = ({ event }) => {
    const userResponse = userResponses[event.id];

    return (
      <div className="event-card">
        <div className="event-image">
          {event.hasImage ? (
            <img src={event.imageUrl} alt={event.name} />
          ) : (
            <div className="event-placeholder-overlay">
              <div className="event-placeholder">{event.imagePlaceholder}</div>
            </div>
          )}

          {event.status === "past" && (
            <div className="event-status-badge">Past Event</div>
          )}
          <div className="photo-count">
            {event.hasImage ? "1 photo" : "No photos"}
          </div>
        </div>

        <div className="event-content">
          <div className="event-header">
            <h3 className="event-title">{event.name}</h3>
            <span
              className={`category-badge ${event.category
                .toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[()]/g, "")}`}
            >
              {event.category}
            </span>
          </div>

          <p className="event-description">{event.description}</p>

          <div className="event-details">
            <div className="event-detail">
              <span className="detail-icon">📅</span>
              <span>
                {event.date} • {event.time}
              </span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">📍</span>
              <span>{event.venue}</span>
            </div>
            <div className="event-detail">
              <span className="detail-icon">👥</span>
              <span>{event.attendees} interested</span>
            </div>
          </div>

          <div className="event-tags">
            <span className="tag">{event.category}</span>
            <span className="tag">{event.eventType}</span>
            <span className="tag">{event.club}</span>
          </div>

          {isStudent() ? (
            <div className="student-actions">
              <button
                className={`action-btn going ${
                  userResponse === "going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "going")}
              >
                ✓ Going
              </button>
              <button
                className={`action-btn not-going ${
                  userResponse === "not-going" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "not-going")}
              >
                ✗ Not Going
              </button>
              <button
                className={`action-btn maybe ${
                  userResponse === "maybe" ? "active" : ""
                }`}
                onClick={() => handleUserResponse(event.id, "maybe")}
              >
                ? Maybe
              </button>
              {event.needsVolunteers && (
                <button
                  className={`action-btn volunteer ${
                    userResponse === "volunteer" ? "active" : ""
                  }`}
                  onClick={() => handleUserResponse(event.id, "volunteer")}
                >
                  🙋‍♂️ Volunteer
                </button>
              )}
            </div>
          ) : (
            <div className="club-actions">
              <button className="action-btn stats">📊 Stats</button>
              <button className="action-btn edit">✏️ Edit</button>
              <button className="action-btn delete">🗑️ Delete</button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Show loading if user data is not yet available
  if (!user) {
    return (
      <div className="campus-events">
        <div className="loading-container">
          <p>Loading user data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="campus-events">
      {/* Header */}
      <div className="header">
        <div className="header-left">
          <div className="app-icon">📅</div>
          <div>
            <h1 className="app-title">Campus Events</h1>
            <p className="app-subtitle">
              ✨ Discover amazing events happening on campus
            </p>
          </div>
        </div>
        <div className="header-right">
          <div className="user-role-display">
            <span className="role-label">
              {isStudent() ? "Student" : isClub() ? "Club" : "User"} View
            </span>
            <span className="role-badge">
              {isStudent() ? "🎓" : isClub() ? "🏛️" : "👤"}
            </span>
          </div>
          <button
            className="filter-toggle"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </button>
        </div>
      </div>

      {/* Floating Quick Stats */}
      <div className="floating-quick-stats">
        <div className="stats-header">
          <TrendingUp className="stats-icon" />
          <span className="stats-title">Quick Stats</span>
        </div>
        <div className="stats-list">
          <div className="stat-item">
            <Calendar className="stat-icon" />
            <span className="stat-label">Total Events</span>
            <span className="stat-number">{events.length}</span>
          </div>
          <div className="stat-item">
            <Star className="stat-icon" />
            <span className="stat-label">
              {isStudent() ? "My RSVPs" : "My Events"}
            </span>
            <span className="stat-number">
              {isStudent() ? getUserRSVPCount() : "0"}
            </span>
          </div>
          <div className="stat-item">
            <Heart className="stat-icon" />
            <span className="stat-label">Upcoming</span>
            <span className="stat-number">{getUpcomingEventsCount()}</span>
          </div>
        </div>
      </div>

      <div className="main-content">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="filters-sidebar">
            <div className="filter-section">
              <h3>Search Events</h3>
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-section">
              <h3>Date Range</h3>
              <input
                type="date"
                className="date-input"
                placeholder="Pick a date"
              />
            </div>

            <div className="filter-section">
              <h3>Club Categories</h3>
              {categories.map((category) => (
                <label key={category} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => handleCategoryChange(category)}
                  />
                  {category}
                </label>
              ))}
            </div>

            <div className="filter-section">
              <h3>Event Types</h3>
              {eventTypes.map((eventType) => (
                <label key={eventType} className="filter-option">
                  <input
                    type="checkbox"
                    checked={selectedEventTypes.includes(eventType)}
                    onChange={() => handleEventTypeChange(eventType)}
                  />
                  {eventType}
                </label>
              ))}
            </div>

            <button className="clear-filters-btn" onClick={clearFilters}>
              Clear All Filters
            </button>
          </div>
        )}

        {/* Events Content */}
        <div className="events-content">
          {/* Tabs */}
          <div className="content-tabs">
            <button
              className={`content-tab ${activeTab === "all" ? "active" : ""}`}
              onClick={() => setActiveTab("all")}
            >
              All Events
            </button>
            <button
              className={`content-tab ${
                activeTab === "upcoming" ? "active" : ""
              }`}
              onClick={() => setActiveTab("upcoming")}
            >
              Upcoming
            </button>
            <button
              className={`content-tab ${activeTab === "past" ? "active" : ""}`}
              onClick={() => setActiveTab("past")}
            >
              Past
            </button>
          </div>

          {/* Events Grid */}
          <div className="events-grid">
            {filteredEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <div className="no-events">
              <p>No events found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CampusEvents;
