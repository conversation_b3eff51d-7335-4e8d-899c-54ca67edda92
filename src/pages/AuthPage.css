body {
  margin: 0;
  font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  min-height: 100vh;
}

.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.auth-box {
  background-color: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 450px;
  text-align: center;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.auth-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #06b6d4 100%);
}

.auth-header {
  margin-bottom: 2rem;
}

.auth-header h1 {
  color: #2563eb;
  margin: 0.5rem 0 0.2rem;
  font-size: 2rem;
  font-weight: 700;
}

.auth-header p {
  font-size: 0.9rem;
  color: #6b7280;
  margin-top: 0;
}

.auth-logo {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 1rem;
}

.user-toggle {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
}

.toggle-btn {
  flex: 1;
  padding: 0.8rem 1.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  background-color: transparent;
  color: #6c757d;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  position: relative;
}

.toggle-btn:hover:not(.active) {
  background-color: #e9ecef;
  color: #495057;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.google-signin-btn {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background-color: white;
  color: #374151;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  box-sizing: border-box;
}

.google-signin-btn:hover:not(:disabled) {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.google-signin-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.google-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: #9ca3af;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #e5e7eb;
}

.divider span {
  padding: 0 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.auth-form input,
.auth-form select {
  width: 100%;
  padding: 0.8rem;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  outline: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background-color: white;
}

.auth-form input:focus,
.auth-form select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.auth-form input:disabled,
.auth-form select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
  color: #9ca3af;
}

/* Form styling for signup */
.data-collection-form {
  text-align: left;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 600;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.8rem;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  outline: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
  color: #9ca3af;
}

.form-group select {
  cursor: pointer;
}

.gradient-button {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  color: white;
  border: none;
  padding: 0.8rem;
  width: 100%;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  box-sizing: border-box;
  margin-top: 0.5rem;
}

.gradient-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
}

.gradient-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 0.8rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 4px solid #dc2626;
  font-size: 0.9rem;
  text-align: left;
  border: 1px solid #fecaca;
}
.success-message {
  background-color: #f0fdf4;
  color: #16a34a;
  padding: 0.8rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 4px solid #16a34a;
  font-size: 0.9rem;
  text-align: left;
  border: 1px solid #bbf7d0;
}

.auth-footer {
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.auth-footer span {
  color: #4f46e5;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.auth-footer span:hover {
  color: #06b6d4;
}

.gradient-text {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* User Info Styles */
.user-info {
  text-align: center;
  padding: 1rem 0;
}

.user-avatar {
  margin-bottom: 1rem;
}

.avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #4f46e5;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 auto;
}

.user-info h3 {
  margin: 0.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.user-email {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0.25rem 0;
}

.user-role {
  margin: 1rem 0;
  font-size: 0.9rem;
  color: #4b5563;
}

.role-badge {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.user-actions .gradient-button {
  padding: 0.7rem;
  font-size: 0.9rem;
}

.loading-spinner {
  font-size: 1.1rem;
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Loading animation */
.loading-spinner::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #4f46e5;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 15px;
  }
  
  .auth-box {
    padding: 2rem;
    max-width: 100%;
  }
  
  .auth-header h1 {
    font-size: 1.8rem;
  }
  
  .toggle-btn {
    padding: 0.7rem 1.2rem;
    font-size: 0.85rem;
  }
  
  .google-signin-btn {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }
  
  .auth-box {
    padding: 1.5rem;
  }
  
  .auth-header h1 {
    font-size: 1.6rem;
  }
  
  .toggle-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
  
  .google-signin-btn {
    font-size: 0.85rem;
    padding: 0.7rem;
  }
  
  .auth-form input,
  .auth-form select,
  .form-group input,
  .form-group select {
    font-size: 0.9rem;
  }
  
  .gradient-button {
    font-size: 0.9rem;
  }
}

@media (max-width: 360px) {
  .auth-box {
    padding: 1.2rem;
  }
  
  .auth-header h1 {
    font-size: 1.4rem;
  }
  
  .auth-logo {
    font-size: 2rem;
  }
  
  .toggle-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
  }
}

/* Focus styles for accessibility */
.toggle-btn:focus,
.google-signin-btn:focus,
.gradient-button:focus,
.auth-form input:focus,
.auth-form select:focus,
.form-group input:focus,
.form-group select:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
.auth-footer span,
.toggle-btn,
.google-signin-btn,
.gradient-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover states */
.auth-footer span:hover {
  text-decoration: none;
}

/* Better form validation styles */
.auth-form input:invalid,
.form-group input:invalid {
  border-color: #dc2626;
}

.auth-form input:valid,
.form-group input:valid {
  border-color: #10b981;
}

/* Improved select styling */
.auth-form select,
.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}
