/* --- Google Font --- */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  background-color: #f9fafb;
  color: #111827;
}

.club-page {
  padding: 2rem;
  max-width: 1280px;
  margin: auto;
}

.club-page h1 {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #3b82f6, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  text-align: center;
  color: #6b7280;
  max-width: 700px;
  margin: 0 auto 2rem;
  font-size: 1.1rem;
}

.search-bar {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.search-bar input {
  padding: 0.8rem 1.2rem;
  border-radius: 9999px;
  border: 1px solid #d1d5db;
  width: 50%;
  font-size: 1rem;
}

.search-bar button {
  padding: 0.8rem 1.5rem;
  background: linear-gradient(90deg, #7b61ff, #b46bff);
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  padding: 0.6rem 1.2rem;
  border-radius: 9999px;
  cursor: pointer;
  font-size: 0.95rem;
  color: #374151;
  transition: all 0.3s ease;
}

.tab.active-tab {
  border: 2px solid #6366f1;
  font-weight: 600;
  background-color: #eef2ff;
}

.featured-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.club-card-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.club-card-featured {
  border-radius: 1rem;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 20px rgba(0,0,0,0.05);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  transition: transform 0.2s ease;
}

.club-card-featured:hover {
  transform: translateY(-3px);
}

.top-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category {
  background: #6366f1;
  color: white;
  padding: 0.3rem 0.8rem;
  font-size: 0.75rem;
  border-radius: 9999px;
  font-weight: 500;
}

.featured-badge {
  background: white;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.bookmark {
  color: white;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 9999px;
  background-color: #f3f4f6;
  cursor: pointer;
  transition: background 0.2s ease;
}

.bookmark:hover {
  background-color: #e5e7eb;
}

.bookmark.active {
  color: black;
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.desc {
  color: #6b7280;
  font-size: 0.95rem;
}

.stats, .time-loc {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #4b5563;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #f1f5f9;
  color: #374151;
  font-size: 0.8rem;
  padding: 0.3rem 0.8rem;
  border-radius: 9999px;
  font-weight: 500;
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.join-btn {
  padding: 0.6rem 1.2rem;
  background: linear-gradient(to right, #3b82f6, #6366f1);
  color: white;
  border: none;
  border-radius: 9999px;
  font-weight: 600;
  cursor: pointer;
  transition: 0.3s ease;
}

.join-btn:hover {
  background: linear-gradient(to right, #2563eb, #4f46e5);
}

.socials {
  display: flex;
  gap: 0.5rem;
}

.social-icon {
  font-size: 1.1rem;
  color: #6b7280;
  transition: transform 0.2s ease, color 0.2s ease;
}

.social-icon:hover {
  transform: scale(1.2);
  color: #3b82f6;
}