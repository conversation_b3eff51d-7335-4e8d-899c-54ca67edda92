import React, { useState, useEffect } from "react";
import { Calendar, Clock, Upload, X, Building } from "lucide-react";
import { useAuth } from "../hook/useAuth";
import { useToast } from "../components/ToastContext.jsx";
import "./CreateEventForm.css";

const CreateEventForm = () => {
  const { addToast } = useToast();
  const { user, supabase } = useAuth();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: "",
    time: "",
    venue: "",
    tags: [],
    rsvpLimit: "",
    poster: null,
    event_type: "optional",
    target_batch_year: "",
    max_volunteers: "",
  });

  const [selectedTags, setSelectedTags] = useState([]);
  const [showTagDropdown, setShowTagDropdown] = useState(false);
  const [clubInfo, setClubInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const availableTags = [
    "workshop",
    "seminar",
    "social",
    "sports",
    "cultural",
    "technical",
    "academic",
    "networking",
    "competition",
    "meeting",
    "conference",
    "party",
  ];

  const eventTypes = [
    { value: "optional", label: "Optional" },
    { value: "compulsory", label: "Compulsory" },
    { value: "batch_compulsory", label: "Batch Compulsory" },
  ];

  // Fetch club data
  const fetchClubData = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      const fallbackData = {
        id: user.id,
        name: user.user_metadata?.club_name || "Unknown Club",
        description: user.user_metadata?.description || "",
        logo_url: null,
        contact_email: user.user_metadata?.club_email || user.email || "",
        contact_phone: user.user_metadata?.contact_phone || "",
        club_category: user.user_metadata?.club_category || "",
        established_year: user.user_metadata?.established_year || null,
        website: user.user_metadata?.website || "",
        is_active: true,
        created_at: user.created_at,
      };

      setClubInfo(fallbackData);
      setLoading(false);

      // Try to fetch from database
      if (supabase) {
        const { data: dbClubData, error: dbError } = await supabase
          .from("clubs")
          .select("*")
          .eq("id", user.id)
          .single();

        if (dbClubData) {
          setClubInfo({
            ...fallbackData,
            name: dbClubData.name || fallbackData.name,
            description: dbClubData.description || fallbackData.description,
            logo_url: dbClubData.logo_url,
            contact_email:
              dbClubData.contact_email || fallbackData.contact_email,
            contact_phone:
              dbClubData.contact_phone || fallbackData.contact_phone,
            is_active: dbClubData.is_active,
            updated_at: dbClubData.updated_at,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching club data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClubData();
  }, [user, supabase]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleTagSelect = (tag) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags((prev) => [...prev, tag]);
      setFormData((prev) => ({ ...prev, tags: [...prev.tags, tag] }));
    }
  };

  const removeTag = (tagToRemove) => {
    setSelectedTags((prev) => prev.filter((tag) => tag !== tagToRemove));
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        addToast({
          type: "error",
          message: "File size must be less than 10MB",
        });
        return;
      }

      // Validate file type
      const allowedTypes = ["image/png", "image/jpeg", "image/gif"];
      if (!allowedTypes.includes(file.type)) {
        addToast({
          type: "error",
          message: "Only PNG, JPG, and GIF files are allowed",
        });
        return;
      }

      setFormData((prev) => ({ ...prev, poster: file }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // Basic validation
      if (
        !formData.title ||
        !formData.description ||
        !formData.date ||
        !formData.time ||
        !formData.venue
      ) {
        addToast({
          type: "error",
          message: "Please fill in all required fields",
        });
        setSubmitting(false);
        return;
      }

      if (!user || !clubInfo) {
        addToast({
          type: "error",
          message: "User or club information not available",
        });
        setSubmitting(false);
        return;
      }

      // Prepare event data for insertion
      const eventData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        event_date: formData.date,
        event_time: formData.time,
        venue: formData.venue.trim(),
        created_by: user.id,
        club_id: clubInfo.id,
        event_type: formData.event_type,
        tags: formData.tags.length > 0 ? formData.tags : null,
        max_volunteers: formData.max_volunteers
          ? parseInt(formData.max_volunteers)
          : 0,
        target_batch_year: formData.target_batch_year
          ? parseInt(formData.target_batch_year)
          : null,
        needs_volunteers:
          formData.max_volunteers && parseInt(formData.max_volunteers) > 0,
        rsvp_limit: formData.rsvpLimit ? parseInt(formData.rsvpLimit) : null,
        is_active: true,
        attendees_count: 0,
        status: "upcoming",
        poster_url: null, // Will be updated after file upload if needed
      };

      // Insert event into database
      const { data: insertedEvent, error: insertError } = await supabase
        .from("events")
        .insert([eventData])
        .select()
        .single();

      if (insertError) {
        console.error("Insert error:", insertError);
        addToast({
          type: "error",
          message: `Failed to create event: ${insertError.message}`,
        });
        setSubmitting(false);
        return;
      }

      // If there's a poster file, upload it
      if (formData.poster && insertedEvent) {
        try {
          const fileExt = formData.poster.name.split(".").pop();
          const fileName = `${insertedEvent.id}-${Date.now()}.${fileExt}`;

          const { data: uploadData, error: uploadError } =
            await supabase.storage
              .from("event-posters")
              .upload(fileName, formData.poster);

          if (uploadError) {
            console.error("Upload error:", uploadError);
            addToast({
              type: "warning",
              message: "Event created but poster upload failed",
            });
          } else {
            // Get public URL and update event record
            const {
              data: { publicUrl },
            } = supabase.storage.from("event-posters").getPublicUrl(fileName);

            await supabase
              .from("events")
              .update({ poster_url: publicUrl })
              .eq("id", insertedEvent.id);
          }
        } catch (uploadError) {
          console.error("Poster upload error:", uploadError);
          addToast({
            type: "warning",
            message: "Event created but poster upload failed",
          });
        }
      }

      // Success
      addToast({
        type: "success",
        message: "Event created successfully!",
      });

      // Reset form
      setFormData({
        title: "",
        description: "",
        date: "",
        time: "",
        venue: "",
        tags: [],
        rsvpLimit: "",
        poster: null,
        event_type: "optional",
        target_batch_year: "",
        max_volunteers: "",
      });
      setSelectedTags([]);
    } catch (error) {
      console.error("Unexpected error:", error);
      addToast({
        type: "error",
        message: `Unexpected error: ${error.message}`,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (
      window.confirm("Are you sure you want to cancel? All data will be lost.")
    ) {
      setFormData({
        title: "",
        description: "",
        date: "",
        time: "",
        venue: "",
        tags: [],
        rsvpLimit: "",
        poster: null,
        event_type: "optional",
        target_batch_year: "",
        max_volunteers: "",
      });
      setSelectedTags([]);
    }
  };

  const getClubInitials = () => {
    if (!clubInfo?.name) return "CL";
    return clubInfo.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return (
      <div className="form-container">
        <div className="form-wrapper">
          <div className="loading-spinner">Loading club information...</div>
        </div>
      </div>
    );
  }

  if (!clubInfo) {
    return (
      <div className="form-container">
        <div className="form-wrapper">
          <div className="error-message">
            Unable to load club information. Please try refreshing the page.
          </div>
        </div>
      </div>
    );
  }

  return (
    <form className="form-container" onSubmit={handleSubmit}>
      <div className="form-wrapper">
        <h1 className="form-title">Create New Event</h1>
        <div className="form-content">
          {/* Club Information Display */}
          <div className="form-group club-info-group">
            <label className="form-label">Club</label>
            <div className="club-info-display">
              <div className="club-info-content">
                <div className="club-avatar">
                  {clubInfo.logo_url ? (
                    <img
                      src={clubInfo.logo_url}
                      alt="Club Logo"
                      className="club-logo"
                    />
                  ) : (
                    <div className="club-initials">{getClubInitials()}</div>
                  )}
                </div>
                <div className="club-details">
                  <span className="club-name">{clubInfo.name}</span>
                  {clubInfo.description && (
                    <span className="club-description">
                      {clubInfo.description}
                    </span>
                  )}
                </div>
                <div className="club-status">
                  <span className="status-indicator active"></span>
                  <span className="status-text">Active</span>
                </div>
              </div>
            </div>
          </div>

          {/* Event Title */}
          <div className="form-group">
            <label className="form-label">
              Event Title <span className="required">*</span>
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter event title"
              required
            />
          </div>

          {/* Description */}
          <div className="form-group">
            <label className="form-label">
              Description <span className="required">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="form-textarea"
              placeholder="Describe your event..."
              required
            />
          </div>

          {/* Date and Time */}
          <div className="form-row">
            <div className="form-group">
              <label className="form-label">
                Date <span className="required">*</span>
              </label>
              <div className="input-with-icon">
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="form-input icon-input"
                  min={new Date().toISOString().split("T")[0]}
                  required
                />
                <Calendar className="input-icon" />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Time <span className="required">*</span>
              </label>
              <div className="input-with-icon">
                <input
                  type="time"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  className="form-input icon-input"
                  required
                />
                <Clock className="input-icon" />
              </div>
            </div>
          </div>

          {/* Venue */}
          <div className="form-group">
            <label className="form-label">
              Venue <span className="required">*</span>
            </label>
            <input
              type="text"
              name="venue"
              value={formData.venue}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter venue location"
              required
            />
          </div>

          {/* Event Type */}
          <div className="form-group">
            <label className="form-label">Event Type</label>
            <select
              name="event_type"
              value={formData.event_type}
              onChange={handleInputChange}
              className="form-input"
            >
              {eventTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Target Batch Year and Max Volunteers */}
          <div className="form-row">
            <div className="form-group">
              <label className="form-label">Target Batch Year</label>
              <input
                type="number"
                name="target_batch_year"
                value={formData.target_batch_year}
                onChange={handleInputChange}
                className="form-input"
                placeholder="e.g., 2025"
                min="2020"
                max="2030"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Max Volunteers</label>
              <input
                type="number"
                name="max_volunteers"
                value={formData.max_volunteers}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Number of volunteers needed"
                min="0"
              />
            </div>
          </div>

          {/* RSVP Limit */}
          <div className="form-group">
            <label className="form-label">RSVP Limit</label>
            <input
              type="number"
              name="rsvpLimit"
              value={formData.rsvpLimit}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Maximum number of attendees"
              min="1"
            />
          </div>

          {/* Tags */}
          <div className="form-group">
            <label className="form-label">Tags</label>
            <div className="tags-container">
              <div
                className="tags-selector"
                onClick={() => setShowTagDropdown(!showTagDropdown)}
              >
                <div className="selected-tags">
                  {selectedTags.length === 0 ? (
                    <span className="placeholder">Select tags</span>
                  ) : (
                    selectedTags.map((tag, index) => (
                      <span key={index} className="tag-chip">
                        {tag}
                        <X
                          className="tag-remove"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeTag(tag);
                          }}
                        />
                      </span>
                    ))
                  )}
                </div>
                <div className="dropdown-arrow">▼</div>
              </div>

              {showTagDropdown && (
                <div className="tags-dropdown">
                  {availableTags.map((tag, index) => (
                    <div
                      key={index}
                      className={`tag-option ${
                        selectedTags.includes(tag) ? "selected" : ""
                      }`}
                      onClick={() => {
                        handleTagSelect(tag);
                        setShowTagDropdown(false);
                      }}
                    >
                      {tag}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Event Poster */}
          <div className="form-group">
            <label className="form-label">Event Poster</label>
            <div
              className="file-upload-area"
              onClick={() => document.getElementById("file-input").click()}
            >
              <input
                type="file"
                id="file-input"
                className="file-input"
                accept="image/png,image/jpeg,image/gif"
                onChange={handleFileUpload}
              />
              <label htmlFor="file-input" className="file-upload-label">
                <Upload className="upload-icon" />
                <p className="upload-text">
                  {formData.poster
                    ? `Selected: ${formData.poster.name}`
                    : "Click to upload a poster"}
                </p>
                <p className="upload-subtext">PNG, JPG, GIF up to 10MB</p>
              </label>
            </div>
          </div>

          {/* Form Buttons */}
          <div className="form-buttons">
            <button
              type="button"
              className="btn btn-cancel"
              onClick={handleCancel}
              disabled={submitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-create"
              disabled={submitting}
            >
              {submitting ? "Creating..." : "Create Event"}
            </button>
          </div>
        </div>
      </div>
    </form>
  );
};

export default CreateEventForm;
