
.campus-buddy-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f9fafb 0%, #dbeafe 100%);
  }

  .hero-section {
    padding: 4rem 1.5rem;
  }
  
  .hero-container {
    max-width: 1024px;
    margin: 0 auto;
    text-align: center;
  }
  
  .hero-title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
  
  .hero-icon {
    background: #2563eb;
    padding: 0.75rem;
    border-radius: 0.75rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .hero-calendar {
    width: 2rem;
    height: 2rem;
    color: white;
  }
  
  .hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #111827;
    margin: 0;
  }
  
  @media (min-width: 768px) {
    .hero-title {
      font-size: 3.75rem;
    }
  }
  
  .title-highlight {
    color: #2563eb;
  }
  
  .hero-subtitle-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .subtitle-star {
    width: 1.25rem;
    height: 1.25rem;
    color: #3b82f6;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
    color: #4b5563;
    font-weight: 500;
    margin: 0;
  }
  
  .hero-description {
    font-size: 1.25rem;
    color: #374151;
    margin: 0 auto 2.5rem;
    max-width: 768px;
    line-height: 1.75;
  }
  
  .hero-cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #2563eb;
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .hero-cta-btn:hover {
    background: #1d4ed8;
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
  }
  
  .cta-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .cta-arrow {
    width: 1.25rem;
    height: 1.25rem;
    transition: transform 0.3s ease;
  }
  
  .hero-cta-btn:hover .cta-arrow {
    transform: translateX(0.25rem);
  }

  .home-stats-section {
    padding: 4rem 1.5rem;
    background: white;
  }
  
  .home-stats-container {
    max-width: 1152px;
    margin: 0 auto;
  }
  
  .home-stats-header {
    text-align: center;
    margin-bottom: 3rem;
  }
  
  .home-stats-title {
    font-size: 2rem;
    font-weight: bold;
    color: #111827;
    margin: 0 0 1rem;
  }
  
  @media (min-width: 768px) {
    .home-stats-title {
      font-size: 2.5rem;
    }
  }
  
  .home-stats-subtitle {
    font-size: 1.125rem;
    color: #4b5563;
    margin: 0;
  }
  
  .home-stats-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
  }
  
  @media (min-width: 768px) {
    .home-stats-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  .home-stat-card {
    position: relative;
    padding: 2rem;
    border-radius: 1rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: white;
  }
  
  .home-stat-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
  }
  
  .home-stat-card-hovered {
    border-color: #60a5fa !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    background: #dbeafe !important;
  }
  
  .home-stat-icon-container {
    display: inline-flex;
    padding: 1rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }
  
  .home-stat-icon-hovered {
    transform: scale(1.1);
  }
  
  .home-stat-icon {
    width: 2rem;
    height: 2rem;
  }
  
  .home-stat-blue .home-stat-icon-container {
    background: #dbeafe;
    color: #2563eb;
  }
  
  .home-stat-green .home-stat-icon-container {
    background: #dcfce7;
    color: #16a34a;
  }
  
  .home-stat-yellow .home-stat-icon-container {
    background: #fef3c7;
    color: #d97706;
  }
  
  .home-stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #1f2937;
    transition: all 0.3s ease;
  }
  
  .home-stat-number-hovered {
    color: #2563eb !important;
    transform: scale(1.1);
  }
  
  .home-stat-label {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem;
  }
  
  .home-stat-description {
    transition: all 0.3s ease;
    overflow: hidden;
    max-height: 0;
    opacity: 0;
  }
  
  .home-stat-description-visible {
    max-height: 5rem;
    opacity: 1;
  }
  
  .home-stat-description p {
    color: #4b5563;
    font-size: 0.875rem;
    margin: 0.5rem 0 0;
  }
  
  .home-stat-trending-icon {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: #2563eb;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    animation: bounce 1s infinite;
  }
  
  .trending-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .features-section {
    padding: 4rem 1.5rem;
  }
  
  .features-container {
    max-width: 1152px;
    margin: 0 auto;
  }
  
  .features-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
  }
  
  @media (min-width: 768px) {
    .features-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  .feature-card {
    position: relative;
    padding: 2rem;
    border-radius: 1rem;
    border: 2px solid;
    transition: all 0.5s ease;
    cursor: pointer;
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
  
  .feature-card:hover {
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
  }
  
  .feature-card-hovered {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    background: white !important;
    transform: scale(1.05) !important;
  }
  
  .feature-blue {
    border-color: #bfdbfe;
  }
  
  .feature-blue:hover {
    border-color: #60a5fa;
  }
  
  .feature-green {
    border-color: #bbf7d0;
  }
  
  .feature-green:hover {
    border-color: #4ade80;
  }
  
  .feature-yellow {
    border-color: #fed7aa;
  }
  
  .feature-yellow:hover {
    border-color: #fb923c;
  }
  
  .feature-icon-container {
    display: inline-flex;
    padding: 1rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.5s ease;
  }
  
  .feature-icon-hovered {
    transform: rotate(6deg) scale(1.1);
  }
  
  .feature-icon {
    width: 3rem;
    height: 3rem;
  }
  
  .feature-blue .feature-icon-container {
    background: #dbeafe;
    color: #2563eb;
  }
  
  .feature-green .feature-icon-container {
    background: #dcfce7;
    color: #16a34a;
  }
  
  .feature-yellow .feature-icon-container {
    background: #fef3c7;
    color: #d97706;
  }
  
  .feature-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 0 1rem;
    color: #1f2937;
    transition: all 0.3s ease;
  }
  
  .feature-title-hovered {
    color: #2563eb;
  }
  
  .feature-description {
    color: #4b5563;
    line-height: 1.75;
    margin: 0;
  }
  
  .feature-gradient-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    border-radius: 1rem;
    animation: pulse 2s infinite;
  }
  
  .feature-star-icon {
    position: absolute;
    top: -0.75rem;
    right: -0.75rem;
    background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    animation: bounce 1s infinite;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  
  .star-icon {
    width: 1rem;
    height: 1rem;
  }
  
  /* CTA Section */
  .cta-section {
    padding: 4rem 1.5rem;
    background: linear-gradient(135deg, #2563eb 0%, #9333ea 100%);
    margin-bottom: -2rem;
  }
  
  .cta-container {
    max-width: 1024px;
    margin: 0 auto;
    text-align: center;
    color: white;
  }
  
  .cta-title {
    font-size: 2rem;
    font-weight: bold;
    margin: 0 0 1.5rem;
  }
  
  @media (min-width: 768px) {
    .cta-title {
      font-size: 2.5rem;
    }
  }
  
  .cta-description {
    font-size: 1.25rem;
    margin: 0 auto 2.5rem;
    opacity: 0.9;
    max-width: 512px;
  }
  
  .cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    color: #2563eb;
    padding: 1rem 2.5rem;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .cta-btn:hover {
    background: #f3f4f6;
    transform: scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
  }
  
  .cta-btn-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .cta-emoji {
    width: 1.25rem;
    height: 1.25rem;
    transition: transform 0.3s ease;
  }
  
  .cta-btn:hover .cta-emoji {
    transform: translateX(0.5rem);
  }
  
  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translateY(0);
    }
    40%, 43% {
      transform: translateY(-10px);
    }
    70% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  @media (max-width: 767px) {
    .hero-title {
      font-size: 2rem;
    }
    
    .hero-description {
      font-size: 1rem;
    }
    
    .home-stats-title {
      font-size: 1.75rem;
    }
    
    .cta-title {
      font-size: 1.75rem;
    }
    
    .cta-description {
      font-size: 1rem;
    }
    
    .home-stat-card,
    .feature-card {
      padding: 1.5rem;
    }
    
    .home-stat-number {
      font-size: 2.5rem;
    }
  }