@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body,
.dashboard-container {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(to right, #e0f2fe, #f8fafc);
  padding: 32px;
  min-height: 100vh;
  color: #1e293b;
}

/* 🟦 Header */
.dashboard-header {
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  padding: 40px 24px;
  border-radius: 16px;
  text-align: center;
  margin-bottom: 40px;
  color: white;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  position: relative;
}

.dashboard-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.dashboard-tagline {
  font-size: 18px;
  font-style: italic;
  opacity: 0.9;
}

.create-event-btn {
  margin-top: 20px;
  background-color: white;
  color: #2563eb;
  padding: 12px 22px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.create-event-btn:hover {
  background-color: #f0f9ff;
  transform: translateY(-2px);
}

/* 📊 Stats */
.stat-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 36px;
  justify-content: center;
}

.stat-card {
  flex: 1 1 250px;
  background: rgba(255, 255, 255, 0.9);
  border-left: 6px solid #3b82f6;
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-label {
  font-size: 16px;
  color: #1e3a8a;
  font-weight: 600;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-top: 6px;
  color: #1e293b;
}

/* 📅 Events */
.event-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.event-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #dbeafe;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: transform 0.3s;
}

.event-card:hover {
  transform: scale(1.01);
}

.event-title {
  font-size: 24px;
  color: #1d4ed8;
  font-weight: 600;
  margin-bottom: 6px;
}

.event-description {
  color: #3b82f6;
  margin-bottom: 8px;
}

.event-info {
  font-size: 14px;
  color: #334155;
  margin-bottom: 12px;
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.event-tag {
  background-color: #e0f2fe;
  color: #1d4ed8;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 999px;
}

/* ✏️ 🗑️ Action buttons */
.event-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-left: 20px;
}

.icon-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.2s;
}

.icon-btn:hover {
  transform: scale(1.1);
  background-color: #f1f5f9;
}

.icon-btn.blue svg {
  color: #2563eb;
}

.icon-btn.red svg {
  color: #ef4444;
}
